import { Inject, Provide } from '@midwayjs/core';
import { Customer } from '../entity/customer.entity';
import { BaseService } from '../common/BaseService';
import { OrderService } from './order.service';
import { Pet, PetAttributes } from '../entity/pet.entity';
import { Order, OrderAttributes } from '../entity/order.entity';
import {
  CustomerAddress,
  CustomerAddressAttrs,
} from '../entity/address.entity';
import {
  AdditionalService,
  DictionaryAttributes,
  Employee,
  OrderDetail,
  Service,
  ServiceType,
  Vehicle,
} from '../entity';
import { FindOptions, Op, fn, col } from 'sequelize';
import { CustomerMembershipCard } from '../entity/customer-membership-card.entity';
import { CustomerCoupon } from '../entity/customer-coupon.entity';
import { DiscountType } from '../entity/order-discount-info.entity';
import { OrderStatus } from '../common/Constant';
import { CustomError } from '../error/custom.error';
import { CreateOrderData } from '../interface';

@Provide()
export class CustomerService extends BaseService<Customer> {
  @Inject()
  orderService: OrderService;

  constructor() {
    super('客户');
  }

  getModel() {
    return Customer;
  }

  async findByPhone(phone: string) {
    return await this.findOne({ where: { phone } });
  }

  async updatePoints(id: number, points: number) {
    const customer = await this.findById(id);
    if (!customer) {
      throw new Error('客户不存在');
    }
    return await customer.update({
      points: customer.points + points,
    });
  }

  /**
   * 获取用户宠物列表
   * @param customerId 用户id
   * @returns 宠物列表
   */
  async getPets(customerId: number) {
    const customer = await Customer.findByPk(customerId, {
      include: [Pet],
    });
    return customer?.pets || [];
  }

  /**
   * 获取用户宠物数量
   * @param customerId 用户id
   * @returns 宠物数量
   */
  async getPetCount(customerId: number) {
    const count = await Pet.count({
      where: { customerId },
    });
    return count;
  }

  /**
   * 获取用户宠物信息
   * @param customerId 用户id
   * @param petId 宠物id
   * @returns 宠物信息
   */
  async getPet(customerId: number, petId: number) {
    const customer = await Customer.findByPk(customerId, {
      include: [Pet],
    });
    return customer?.pets?.find(pet => pet.id === petId);
  }

  /**
   * 新增宠物
   * @param customerId 用户id
   * @param pet 宠物信息
   * @returns 宠物信息
   */
  async addPet(customerId: number, pet: Omit<PetAttributes, 'id'>) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    return await Pet.create({
      ...pet,
      customerId,
    });
  }

  /**
   * 更新宠物信息
   * @param customerId 用户id
   * @param petId 宠物id
   * @param pet 宠物信息
   * @returns 宠物信息
   */
  async updatePet(
    customerId: number,
    petId: number,
    pet: Partial<PetAttributes>
  ) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    const petInfo = await Pet.findByPk(petId);
    if (!petInfo) {
      throw new Error('宠物信息不存在');
    }
    return await petInfo.update(pet);
  }

  /**
   * 删除宠物信息
   * @param customerId 用户id
   * @param petId 宠物id
   * @returns 宠物信息
   */
  async deletePet(customerId: number, petId: number) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    const petInfo = await Pet.findByPk(petId);
    if (!petInfo) {
      throw new Error('宠物信息不存在');
    }
    return await petInfo.destroy();
  }

  /**
   * 获取用户地址列表
   * @param customerId 用户id
   * @returns 地址列表
   */
  async getAddressList(customerId: number) {
    const addressList = await CustomerAddress.findAll({
      where: {
        customerId,
      },
    });
    return addressList;
  }

  /**
   * 获取用户默认地址信息
   * @param customerId 用户id
   * @returns 地址信息
   */
  async getDefaultAddress(customerId: number) {
    const address = await CustomerAddress.findOne({
      where: {
        customerId,
        isDefault: true,
      },
    });
    return address;
  }

  /**
   * 新增地址信息
   * @param customerId 用户id
   * @param address 地址信息
   * @returns 地址信息
   */
  async addAddress(
    customerId: number,
    address: Omit<CustomerAddressAttrs, 'id'>
  ) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    return await CustomerAddress.create({
      ...address,
      customerId,
    });
  }

  /**
   * 更新地址信息
   * @param customerId 用户id
   * @param addressId 地址id
   * @param address 地址信息
   * @returns 地址信息
   */
  async updateAddress(
    customerId: number,
    addressId: number,
    address: Partial<CustomerAddressAttrs>
  ) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    const addressInfo = await CustomerAddress.findByPk(addressId);
    if (!addressInfo) {
      throw new Error('地址信息不存在');
    }
    return await addressInfo.update(address);
  }

  /**
   * 删除地址信息
   * @param customerId 用户id
   * @param addressId 地址id
   * @returns 地址信息
   */
  async deleteAddress(customerId: number, addressId: number) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    const addressInfo = await CustomerAddress.findByPk(addressId);
    if (!addressInfo) {
      throw new Error('地址信息不存在');
    }
    return await addressInfo.destroy();
  }

  /**
   * 设置默认地址
   * @param customerId 用户id
   * @param addressId 地址id
   * @returns 地址信息
   */
  async setDefaultAddress(customerId: number, addressId: number) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    const addressInfo = await CustomerAddress.findByPk(addressId);
    if (!addressInfo) {
      throw new Error('地址信息不存在');
    }
    await CustomerAddress.update(
      {
        isDefault: false,
      },
      {
        where: {
          customerId,
        },
      }
    );
    await CustomerAddress.update(
      {
        isDefault: true,
      },
      {
        where: {
          id: addressId,
        },
      }
    );
    return true;
  }

  async getOrders({
    customerId,
    status,
    currentPage,
    pageSize,
  }: {
    customerId: number;
    status?: DictionaryAttributes[];
    currentPage?: number;
    pageSize?: number;
  }) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }

    const where = { customerId };
    if (status) {
      where['status'] = status;
    }
    const options: FindOptions<OrderAttributes> = {
      where,
      include: [
        {
          model: OrderDetail,
          include: [Service, AdditionalService],
        },
        {
          model: Employee,
        },
      ],
      order: [['updatedAt', 'DESC']],
    };
    if (currentPage && pageSize) {
      options.limit = pageSize;
      options.offset = (currentPage - 1) * pageSize;
    }
    const orders = await Order.findAll(options);

    // 兼容分页与不分页两种方式
    if (currentPage && pageSize) {
      // 计算总数
      const count = await Order.count({
        where: { customerId },
      });
      return {
        total: count,
        currentPage: currentPage,
        pageSize: pageSize,
        list: orders,
      };
    } else {
      return orders;
    }
  }

  async getOrder(customerId: number, id: number) {
    const customer = await Customer.findByPk(customerId, {
      include: [
        {
          model: Order,
          include: [
            {
              model: OrderDetail,
              include: [Service, AdditionalService],
            },
          ],
        },
      ],
    });
    return customer?.orders?.find(order => order.id === id);
  }

  async getOrderStatus(customerId: number, sn: string) {
    // 验证用户是否存在
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    const order = await Order.findOne({
      where: { sn },
    });
    return order?.status;
  }

  async createOrder(customerId: number, order: CreateOrderData) {
    // 验证用户是否存在
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }

    // 确保订单中的customerId与路径参数一致
    order.customerId = customerId;

    // 处理优惠信息
    if (order.discountInfos && order.discountInfos.length > 0) {
      // 验证优惠信息的有效性
      for (const discountInfo of order.discountInfos) {
        if (discountInfo.discountType === DiscountType.MEMBERSHIP_CARD) {
          // 验证权益卡是否存在且属于该用户
          const card = await CustomerMembershipCard.findOne({
            where: {
              id: discountInfo.discountId,
              customerId,
              status: 'active',
            },
          });
          if (!card) {
            throw new Error('权益卡不存在或已失效');
          }
        } else if (discountInfo.discountType === DiscountType.COUPON) {
          // 验证代金券是否存在且属于该用户
          const coupon = await CustomerCoupon.findOne({
            where: {
              id: discountInfo.discountId,
              customerId,
              status: 'active',
            },
          });
          if (!coupon) {
            throw new Error('代金券不存在或已失效');
          }
        }
      }
    }

    // 调用订单服务创建订单
    return await this.orderService.createOrder(order);
  }

  async payOrder(customerId: number, sn: string) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    return await this.orderService.payOrder(customerId, sn);
  }

  async applyRefund(customerId: number, sn: string) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    return await this.orderService.applyRefund(customerId, sn);
  }

  async cancelOrder(customerId: number, id: number) {
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new Error('用户信息不存在');
    }
    return await this.orderService.cancelOrder(customerId, id);
  }

  /**
   * 更新用户会员状态
   * @param customerId 用户ID
   * @param memberStatus 会员状态：0-非会员 1-会员
   */
  async updateMemberStatus(customerId: number, memberStatus: number) {
    const customer = await this.findById(customerId);
    if (!customer) {
      throw new Error('用户不存在');
    }
    return await customer.update({ memberStatus });
  }

  /**
   * 检查用户是否有有效的权益卡
   * @param customerId 用户ID
   * @returns 是否有有效的权益卡
   */
  async hasActiveCard(customerId: number): Promise<boolean> {
    const count = await CustomerMembershipCard.count({
      where: {
        customerId,
        status: 'active',
      },
    });
    return count > 0;
  }

  /**
   * 更新所有用户的会员状态
   * 根据是否有有效的权益卡来更新用户的会员状态
   */
  async updateAllMemberStatus() {
    // 获取所有用户
    const customers = await this.findAll({
      query: {},
    });

    // 遍历用户，更新会员状态
    for (const customer of customers.list) {
      const hasActiveCard = await this.hasActiveCard(customer.id);
      const newMemberStatus = hasActiveCard ? 1 : 0;

      // 如果会员状态需要更新，则更新
      if (customer.memberStatus !== newMemberStatus) {
        await customer.update({ memberStatus: newMemberStatus });
      }
    }
  }

  /**
   * 查询指定宠物最后一次洗护完成时间
   * @param customerId 客户ID
   * @param petId 宠物ID
   * @returns 最后一次洗护完成时间信息
   */
  async getLastServiceTime(customerId: number, petId: number) {
    // 验证客户是否存在
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new CustomError('客户不存在');
    }

    // 验证宠物是否存在且属于该客户
    const pet = await Pet.findOne({
      where: {
        id: petId,
        customerId,
      },
    });
    if (!pet) {
      throw new CustomError('宠物不存在或不属于该客户');
    }

    // 查询该宠物最后一次已完成的订单
    // 修复：包含"已完成"和"已评价"状态，并优化排序逻辑
    const lastCompletedOrder = await Order.findOne({
      where: {
        customerId,
        status: [OrderStatus.已完成, OrderStatus.已评价],
      },
      include: [
        {
          model: OrderDetail,
          where: {
            petId,
          },
          required: true,
        },
      ],
      // 修复：使用 COALESCE 函数处理 null 值，优先按 actualServiceEndTime 排序，null 值使用 updatedAt
      order: [
        [fn('COALESCE', col('actualServiceEndTime'), col('updatedAt')), 'DESC'],
      ],
      attributes: [
        'id',
        'sn',
        'actualServiceEndTime',
        'serviceTime',
        'updatedAt',
      ],
    });

    if (!lastCompletedOrder) {
      return {
        petId,
        petName: pet.name,
        lastServiceTime: null,
        message: '该宠物暂无完成的洗护记录',
      };
    }

    return {
      petId,
      petName: pet.name,
      lastServiceTime: lastCompletedOrder.actualServiceEndTime,
      orderId: lastCompletedOrder.id,
      orderSn: lastCompletedOrder.sn,
      serviceTime: lastCompletedOrder.serviceTime,
    };
  }

  /**
   * 获取可选择的员工列表
   * @param customerId 客户ID
   * @param serviceTypeId 服务类型ID（可选，用于筛选）
   * @param current 当前页码
   * @param pageSize 每页大小
   * @param name 员工姓名（可选，用于模糊搜索）
   * @returns 员工列表
   */
  async getAvailableEmployees(
    customerId: number,
    serviceTypeId?: number,
    current = 1,
    pageSize = 10,
    name?: string
  ) {
    // 验证客户是否存在
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new CustomError('客户不存在');
    }

    // 确保分页参数是数字类型
    const currentPage = Number(current) || 1;
    const pageSizeNum = Number(pageSize) || 10;

    const offset = (currentPage - 1) * pageSizeNum;
    const limit = pageSizeNum;

    // 构建查询条件
    const whereCondition: any = {
      status: 1, // 只查询在职的员工
    };

    // 如果提供了姓名，添加模糊搜索条件
    if (name) {
      whereCondition.name = {
        [Op.like]: `%${name}%`,
      };
    }

    // 构建include条件
    const includeConditions: any[] = [
      {
        model: Vehicle,
        required: false, // 左连接，允许员工没有车辆
      },
    ];

    // 如果指定了服务类型，验证服务类型是否存在
    if (serviceTypeId) {
      const serviceType = await ServiceType.findByPk(serviceTypeId);
      if (!serviceType) {
        throw new CustomError('服务类型不存在');
      }
      // 注意：这里暂时不根据服务类型筛选员工，显示所有可用员工供用户选择
      // 具体的服务能力筛选可以在后续版本中根据业务需求添加
    }

    const { rows, count } = await Employee.findAndCountAll({
      where: whereCondition,
      attributes: [
        'id',
        'name',
        /** 手机号 */
        'phone',
        /** 头像 */
        'avatar',
        /** 职位 */
        'position',
        /** 接单等级（1-5级） */
        'level',
        /** 工作经验（月） */
        'workExp',
        /** 服务评分（0-5分） */
        'rating',
      ],
      offset,
      limit,
      order: [
        ['rating', 'DESC'], // 按评分降序
        ['workExp', 'DESC'], // 按工作经验降序
        ['level', 'DESC'], // 按等级降序
      ],
      include: includeConditions,
    });

    return {
      list: rows,
      total: count,
      current: currentPage,
      pageSize: pageSizeNum,
    };
  }
}
